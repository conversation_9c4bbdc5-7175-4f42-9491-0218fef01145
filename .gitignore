# Byte-compiled / optimized / DLL files
__pycache__/
*.py[codz]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py.cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# UV
#   Similar to Pipfile.lock, it is generally recommended to include uv.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#uv.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock
#poetry.toml

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#   pdm recommends including project-wide configuration in pdm.toml, but excluding .pdm-python.
#   https://pdm-project.org/en/latest/usage/project/#working-with-version-control
#pdm.lock
#pdm.toml
.pdm-python
.pdm-build/

# pixi
#   Similar to Pipfile.lock, it is generally recommended to include pixi.lock in version control.
#pixi.lock
#   Pixi creates a virtual environment in the .pixi directory, just like venv module creates one
#   in the .venv directory. It is recommended not to include this directory in version control.
.pixi

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.env.*
.envrc
.venv
.venv*/
.venv311/
env/
venv/
venv*/
ENV/
env.bak/
venv.bak/

# Trading Bot Environment Files
.env.trading
.env.binance
.env.groq
.env.api
.env.keys
.env.secrets
.env.production
.env.development
.env.test
.env.local
.env.staging

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be found at https://github.com/github/gitignore/blob/main/Global/JetBrains.gitignore
#  and can be added to the global gitignore or merged into this file.  For a more nuclear
#  option (not recommended) you can uncomment the following to ignore the entire idea folder.
#.idea/

# Abstra
# Abstra is an AI-powered process automation framework.
# Ignore directories containing user credentials, local state, and settings.
# Learn more at https://abstra.io/docs
.abstra/

# Visual Studio Code
#  Visual Studio Code specific template is maintained in a separate VisualStudioCode.gitignore 
#  that can be found at https://github.com/github/gitignore/blob/main/Global/VisualStudioCode.gitignore
#  and can be added to the global gitignore or merged into this file. However, if you prefer, 
#  you could uncomment the following to ignore the entire vscode folder
# .vscode/

# Ruff stuff:
.ruff_cache/

# PyPI configuration file
.pypirc

# Cursor
#  Cursor is an AI-powered code editor. `.cursorignore` specifies files/directories to
#  exclude from AI features like autocomplete and code analysis. Recommended for sensitive data
#  refer to https://docs.cursor.com/context/ignore-files
.cursorignore
.cursorindexingignore

# Marimo
marimo/_static/
marimo/_lsp/
__marimo__/

# ============================================================================
# FluxTrader Project Specific
# ============================================================================

# Configuration files with sensitive data
config/
configs/
*.config.json
*.config.yaml
*.config.yml
.env.local
.env.production
.env.development
.env.test

# Trading Bot Configuration Files
config.json.backup
config.json.local
config.json.prod
config.json.dev
trading_config.json
binance_config.json
api_config.json
bot_config.json
settings.json
settings.local.json

# API Keys and Secrets
api_keys.json
secrets.json
credentials.json
binance_keys.json
groq_keys.json
trading_keys.json
*.key
*.pem
*.p12
*.pfx
*.keystore
*.jks

# Trading data and logs
data/
logs/
trading_logs/
backtest_results/
*.csv
*.xlsx
*.parquet

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary and cache files
temp/
tmp/
cache/
.cache/
*.tmp
*.temp

# Python virtual environments (all variations)
.venv/
.venv*/
.venv311/
.venv312/
.venv313/
venv/
venv*/
env/
env*/
ENV/
ENV*/
virtualenv/
.virtualenv/
.conda/
conda-env/

# ============================================================================
# Node.js / React Frontend
# ============================================================================

# Dependencies - Node.js modules (all locations)
node_modules/
*/node_modules/
**/node_modules/
react-frontend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
yarn.lock
.yarn/
.pnp.*
package-lock.json
yarn.lock
pnpm-lock.yaml

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm
.npm/

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Package manager lock files (optional - uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Package manager directories
.pnpm-store/
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
.tmp
.temp

# ============================================================================
# React Specific
# ============================================================================

# React build output
react-frontend/build/
react-frontend/dist/
build/
dist/

# React development
react-frontend/.env
react-frontend/.env.local
react-frontend/.env.development.local
react-frontend/.env.test.local
react-frontend/.env.production.local

# React testing
react-frontend/coverage/
react-frontend/src/**/*.test.js
react-frontend/src/**/*.test.jsx
react-frontend/src/**/*.test.ts
react-frontend/src/**/*.test.tsx
react-frontend/src/**/*.spec.js
react-frontend/src/**/*.spec.jsx
react-frontend/src/**/*.spec.ts
react-frontend/src/**/*.spec.tsx

# React Storybook
react-frontend/.storybook/
react-frontend/storybook-static/

# Create React App
react-frontend/public/static/
react-frontend/src/serviceWorker.js

# React DevTools
react-frontend/.vscode/
.vscode/

# Webpack
webpack-stats.json
webpack.config.js.map

# Babel
.babelrc.js
babel.config.js.map

# ESLint
.eslintrc.js.map
.eslintignore

# Prettier
.prettierrc.js
.prettierignore

# TypeScript
react-frontend/tsconfig.json.tsbuildinfo
tsconfig.tsbuildinfo

# Source maps
*.map
*.js.map
*.css.map

# Hot reload
.hot-update.json
*.hot-update.js

# Bundle analyzer
bundle-analyzer-report.html
stats.json

# PWA
react-frontend/public/sw.js
react-frontend/public/workbox-*.js
react-frontend/public/precache-manifest.*.js

# React Router
react-frontend/src/routes.generated.js

# Redux DevTools
redux-devtools-extension/

# Material-UI
.mui-cache/

# Styled Components
.styled-components/

# React Native (if applicable)
*.jks
*.p8
*.p12
*.mobileprovision
*.orig.*
web-build/

# Metro
.metro-health-check*

# Flipper
flipper-rn-addons.json

# CocoaPods
ios/Pods/
ios/Podfile.lock

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Android
android/app/build/
android/build/
android/.gradle/
android/local.properties
android/keystore.properties

# ============================================================================
# Testing and Development
# ============================================================================

# Test files and directories
test_*.py
*_test.py
test_results/
pytest_cache/
.pytest_cache/
htmlcov/
coverage.xml
.coverage
pytest-results.xml
bandit-report.json
safety-report.json
trivy-report.json

# Requirements backup files
requirements-dev.txt
requirements.bak
requirements.old
requirements-*.txt

# Documentation (removed from project)
docs/
*.md.bak

# Development and debugging
debug/
debug.log
*.debug

# IDE and Editor files
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json
.vscode/extensions.json
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ============================================================================
# MCP and Server Files
# ============================================================================

# MCP server logs and data
mcp_logs/
mcp_data/
server_logs/
mcp_server_data/
fastmcp_logs/
binance_mcp_logs/

# Trading Bot Runtime Files
trading_bot.pid
fluxtrader.pid
mcp_server.pid
*.session
*.state
*.runtime
trading_session.json
bot_state.json
agent_state.json

# Process and runtime files
*.pid
*.sock
*.lock

# ============================================================================
# Backup and Archive Files
# ============================================================================

# Backup files
*.bak
*.backup
*.old
*.orig
*.save

# Archive files
*.zip
*.tar
*.tar.gz
*.tar.bz2
*.rar
*.7z

# ============================================================================
# Project Specific Temporary Files
# ============================================================================

# Test scripts and temporary files created during development
test_mcp_client.py
simple_mcp_server.py
mcp_agent_server.py
mcp_server_app.py

# Accidental version number files (from failed pip/package commands)
=*
=*.*.*
=*.*.*.*

# Jupyter notebooks (if any)
*.ipynb

# Documentation build
docs/build/
docs/_build/
